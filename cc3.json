{"agency": {"name": "Content Creation Agency", "description": "Demonstrates a multi-agent workflow for content creation", "logging": {"level": "basic", "tracing": true}}, "brief": {"content-creation-brief": {"title": "Content Creation Brief", "overview": "Create a polished blog post based on the provided prompt.", "background": "The content creation process involves research, drafting, and refining to ensure high-quality output.", "objective": "Produce a well-researched, engaging, and polished blog post.", "targetAudience": "General audience interested in music and pop culture."}}, "agents": {"prompt-researcher": {"id": "prompt-researcher", "name": "Prompt Researcher", "description": "Specializes in finding and analyzing relevant information based on a prompt", "role": "You are a research specialist. Your task is to find relevant information based on the provided prompt. Be thorough in your research and provide structured, actionable insights.", "goals": ["Find comprehensive information based on the prompt", "Provide structured research results"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.4, "maxOutputTokens": 3072}, "tools": {"webSearch": "webSearch"}}, "writer": {"id": "writer", "name": "Writer", "description": "Specializes in drafting content based on research", "role": "You are a content writer. Your task is to draft engaging and informative content based on the research provided. Ensure the content is well-structured and aligned with the prompt.", "goals": ["Draft engaging and informative content", "Ensure the content is well-structured and aligned with the prompt"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.5, "maxOutputTokens": 2048}}, "refiner": {"id": "refiner", "name": "Refiner", "description": "Specializes in refining and polishing content", "role": "You are a content refiner. Your task is to refine and polish the draft to improve clarity, coherence, and quality. Ensure the final content is polished and ready for publication.", "goals": ["Refine and polish the draft for clarity and coherence", "Ensure the final content is polished and ready for publication"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.5, "maxOutputTokens": 2048}}}, "teams": {"content-creation-team": {"name": "Content Creation Team", "description": "Collaborates to create polished content based on a prompt", "agents": {"prompt-researcher": "prompt-researcher", "writer": "writer", "refiner": "refiner"}, "jobs": {"research": {"agent": "prompt-researcher", "description": "Research relevant information based on the prompt", "inputs": {"prompt": "{{initialInputs.prompt}}"}}, "draft": {"agent": "writer", "description": "Draft content based on the research", "inputs": {"research": "{{research}}"}}, "refine": {"agent": "refiner", "description": "Refine and polish the draft", "inputs": {"draft": "{{draft}}"}}}, "workflow": ["research", "draft", "refine"]}}, "orchestrator": {"name": "Workflow Orchestrator", "description": "Manages the multi-agent content creation workflow.", "role": "You are the Workflow Orchestrator for the 'Content Creation Agency'. Your primary task is to manage the collaboration of a team of specialized agents to produce a polished blog post. The workflow is a sequential process and you must ensure each agent completes its task before the next agent begins. Follow these steps precisely:\n\n### Step-by-Step Execution\n\n1.  Initiate Research: Start by assigning the initial prompt to the Prompt Researcher agent. Instruct the researcher to conduct a thorough web search and gather all relevant information. Wait for their structured research results.\n\n2.  Drafting: Once the research is complete, take the research findings and provide them to the Writer agent. Instruct the writer to draft an engaging and informative blog post based on the provided research. Ensure the draft aligns with the target audience and objectives outlined in the brief. Wait for the complete draft.\n\n3.  Refinement: After the draft is received, pass it to the Refiner agent. Instruct the refiner to meticulously review the draft, focusing on improving clarity, coherence, and overall quality. The goal is to polish the content, correct any errors, and make it ready for publication. Wait for the final, polished blog post.\n\n### Final Output\n\nUpon receiving the refined content from the Refiner, your job is complete. Present the final, polished blog post as the final output of the entire process."}}
import { AgentFactory } from './AgentFactory.js';
import { webSearchTool } from './tools/search_tool.js';
import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load the agent configuration from h.json
const hConfig = JSON.parse(readFileSync(new URL('./h.json', import.meta.url)));

// Initialize AgentFactory with API keys
const agentFactory = new AgentFactory({
  defaultProvider: 'gemini', // Assuming gemini as default based on h.json
  apiKeys: {
    gemini: process.env.GEMINI_API_KEY,
    anthropic: process.env.ANTHROPIC_API_KEY,
    openai: process.env.OPENAI_API_KEY,
    groq: process.env.GROQ_API_KEY,
    openrouter: process.env.OPENROUTER_API_KEY,
    mistral: process.env.MISTRAL_API_KEY
  }
});

// Register the web search tool if specified in h.json
const agentDefinition = hConfig['prompt-researcher'];
if (agentDefinition.tools && agentDefinition.tools.webSearch) {
  agentFactory.registerTool(webSearchTool);
}

// Create the agent
const agent = agentFactory.createAgent({
  id: agentDefinition.id,
  name: agentDefinition.name,
  description: agentDefinition.description,
  role: agentDefinition.role,
  goals: agentDefinition.goals,
  provider: agentDefinition.provider,
  llmConfig: agentDefinition.llmConfig,
  tools: agentDefinition.tools
});

// Function to run the agent with a prompt
async function runAgent(agentInstance, prompt) {
  console.log(`🚀 Running agent: ${agentInstance.name}`);
  console.log('----------------------------------------------');
  console.log(`Prompt: ${prompt}`);

  try {
    const result = await agentInstance.handleRequest(prompt);
    console.log('✅ Agent response received!');
    console.log('\n📊 Agent Result:');
    if (typeof result === 'string') {
      console.log(result);
    } else if (result && result.candidates && result.candidates[0] && result.candidates[0].content) {
      console.log(result.candidates[0].content.parts[0].text);
    } else {
      console.log('Result:', result);
    }
  } catch (error) {
    console.error(`❌ Agent execution failed for ${agentInstance.name}:`, error.message);
    throw error;
  }
}

// Main execution
(async () => {
  try {
    const examplePrompt = 'What are the latest advancements in AI for medical diagnosis?';
    await runAgent(agent, examplePrompt);
    console.log('🎉 Agent run completed!');
  } catch (error) {
    console.error('⚠️ Agent run completed with errors:', error.message);
  }
})();

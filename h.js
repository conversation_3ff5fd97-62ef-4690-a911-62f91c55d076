import { AgentFactory } from './AgentFactory.js';
import { webSearchTool } from './tools/search_tool.js';
import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load the agent configuration from h.json
const hConfig = JSON.parse(readFileSync(new URL('./h.json', import.meta.url)));

// Initialize AgentFactory with API keys
const agentFactory = new AgentFactory({
  defaultProvider: 'gemini', // Assuming gemini as default based on h.json
  apiKeys: {
    gemini: process.env.GEMINI_API_KEY,
    anthropic: process.env.ANTHROPIC_API_KEY,
    openai: process.env.OPENAI_API_KEY,
    groq: process.env.GROQ_API_KEY,
    openrouter: process.env.OPENROUTER_API_KEY,
    mistral: process.env.MISTRAL_API_KEY
  }
});

// Register the web search tool if specified in h.json
const agentDefinition = hConfig.agents['prompt-researcher'];
if (agentDefinition.tools && agentDefinition.tools.webSearch) {
  agentFactory.registerTool(webSearchTool);
}

// Create the agent
const agent = agentFactory.createAgent({
  id: agentDefinition.id,
  name: agentDefinition.name,
  description: agentDefinition.description,
  role: agentDefinition.role,
  goals: agentDefinition.goals,
  provider: agentDefinition.provider,
  llmConfig: agentDefinition.llmConfig,
  tools: agentDefinition.tools
});

// Add CoT (Chain of Thought) logging by listening to agent events
function setupCoTLogging(agentInstance) {
  console.log('🧠 Setting up Chain of Thought logging...\n');

  agentInstance.events.on('runStarted', (data) => {
    console.log('🚀 [CoT] Agent run started');
    console.log(`   Input: ${data.input}`);
    console.log(`   Timestamp: ${data.timestamp.toISOString()}\n`);
  });

  agentInstance.events.on('inputFormatted', (data) => {
    console.log('📝 [CoT] Input formatted for LLM');
    console.log(`   Formatted input structure: ${JSON.stringify(data.formattedInput, null, 2)}\n`);
  });

  agentInstance.events.on('llmResponseReceived', (data) => {
    console.log('🤖 [CoT] LLM response received');
    const response = data.llmResponse;
    if (response?.candidates?.[0]?.content?.parts) {
      const textParts = response.candidates[0].content.parts.filter(p => p.text);
      const toolParts = response.candidates[0].content.parts.filter(p => p.functionCall);

      if (textParts.length > 0) {
        console.log('   Text response:');
        textParts.forEach(part => {
          console.log(`   "${part.text}"`);
        });
      }

      if (toolParts.length > 0) {
        console.log('   Tool calls detected:');
        toolParts.forEach(part => {
          console.log(`   - ${part.functionCall.name}(${JSON.stringify(part.functionCall.args)})`);
        });
      }
    }
    console.log('');
  });

  agentInstance.events.on('toolCallsDetected', (data) => {
    console.log('🔧 [CoT] Tool calls detected and being processed');
    data.toolCalls.forEach((call, index) => {
      console.log(`   Tool ${index + 1}: ${call.functionCall.name}`);
      console.log(`   Parameters: ${JSON.stringify(call.functionCall.args, null, 2)}`);
    });
    console.log('');
  });

  agentInstance.events.on('toolCallsHandled', (data) => {
    console.log('✅ [CoT] Tool calls completed');
    data.toolResults.forEach((result, index) => {
      console.log(`   Tool ${index + 1} (${result.toolName}):`);
      if (result.error) {
        console.log(`   ❌ Error: ${result.error}`);
      } else {
        console.log(`   ✅ Result: ${typeof result.result === 'string' ? result.result.substring(0, 200) + '...' : JSON.stringify(result.result).substring(0, 200) + '...'}`);
      }
    });
    console.log('');
  });

  agentInstance.events.on('statusChanged', (data) => {
    console.log(`📊 [CoT] Agent status changed: ${data.oldStatus} → ${data.newStatus}`);
    console.log(`   Timestamp: ${data.timestamp.toISOString()}\n`);
  });

  agentInstance.events.on('runCompleted', (data) => {
    console.log('🎯 [CoT] Agent run completed successfully');
    console.log(`   Final response type: ${typeof data.response}`);
    console.log(`   Timestamp: ${data.timestamp.toISOString()}\n`);
  });

  agentInstance.events.on('runError', (data) => {
    console.log('❌ [CoT] Agent run encountered an error');
    console.log(`   Error: ${data.error}`);
    console.log(`   Timestamp: ${data.timestamp.toISOString()}\n`);
  });
}

// Setup CoT logging for the agent
setupCoTLogging(agent);

// Function to run the agent with a prompt
async function runAgent(agentInstance, prompt) {
  console.log(`🚀 Running agent: ${agentInstance.name}`);
  console.log('='.repeat(80));
  console.log(`📝 Prompt: ${prompt}`);
  console.log('='.repeat(80));
  console.log('');

  try {
    console.log('🧠 CHAIN OF THOUGHT STEPS:');
    console.log('-'.repeat(50));

    const result = await agentInstance.run(prompt);

    console.log('='.repeat(80));
    console.log('🎯 FINAL RESULT:');
    console.log('='.repeat(80));

    if (typeof result === 'string') {
      console.log(result);
    } else if (result && result.finalResponse) {
      // Handle combined response with tool results
      if (result.toolResults) {
        console.log('� Tool Results Summary:');
        console.log(result.toolResults);
        console.log('\n📄 Final Response:');
      }
      console.log(result.finalResponse);
    } else if (result && result.candidates && result.candidates[0] && result.candidates[0].content) {
      console.log(result.candidates[0].content.parts[0].text);
    } else {
      console.log('Raw Result:', JSON.stringify(result, null, 2));
    }

    console.log('\n' + '='.repeat(80));

  } catch (error) {
    console.error(`❌ Agent execution failed for ${agentInstance.name}:`, error.message);
    throw error;
  }
}

// Main execution
(async () => {
  try {
    const examplePrompt = 'What are the latest advancements in AI for medical diagnosis?';
    await runAgent(agent, examplePrompt);
    console.log('🎉 Agent run completed!');
  } catch (error) {
    console.error('⚠️ Agent run completed with errors:', error.message);
  }
})();
